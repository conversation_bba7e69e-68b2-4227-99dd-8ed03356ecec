{
    "version": "0.2.0",
    "configurations": [
        {
            "type": "lldb",
            "request": "launch",
            "name": "Debug executable 'grrs'",
            "cargo": {
                "args": [
                    "build",
                    "--bin=proxy-rs",
                    "--package=proxy-rs",
                ],
                "filter": {
                    "name": "proxy-rs",
                    "kind": "bin"
                }
            },
            "env": {
                "RUST_BACKTRACE": "full",
                "RUST_LOG": "debug"
            },
            "args": [],
            "cwd": "${workspaceFolder}",
            "sourceLanguages": [
                "rust"
            ],
            // "console": "internalConsole"
        },
        {
            "type": "lldb-dap",
            "request": "launch",
            "name": "lldb-dap Debug executable 'grrs'",
            "program": "${workspaceFolder}/target/debug/grrs",
            "env": {
                "RUST_BACKTRACE": "full"
            },
            "args": [
                "a",
                "/Users/<USER>/Data/RustProjects/grrs/Cargo.toml"
            ],
            "cwd": "${workspaceFolder}",
            "preLaunchTask": "rust: cargo build",
            "debugAdapterArgs": [
                "--ferwrgfrewg",
                "--one-line-before-file",
                "command script import \"/Users/<USER>/.rustup/toolchains/stable-aarch64-apple-darwin/lib/rustlib/etc/lldb_lookup.py\"",
                "--source-before-file",
                "/Users/<USER>/.rustup/toolchains/stable-aarch64-apple-darwin/lib/rustlib/etc/lldb_commands",
                "$@"
            ]
            // "preRunCommands": [
            //     "type summary add -s \"${var.data_ptr} ${var.length}\" std::string::String",
            //     "type summary add -s \"${var.data_ptr} ${var.length}\" alloc::string::String",
            //     "type summary add -s \"${var.data_ptr} ${var.length}\" str",
            //     "type summary add -s \"len=${var.len}\" std::vec::Vec",
            //     "type summary add -s \"${var.some}\" std::option::Option",
            //     "type summary add -s \"${var.ok}\" std::result::Result"
            // ]
        }
    ]
}