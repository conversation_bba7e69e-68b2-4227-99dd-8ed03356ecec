[package]
name = "proxy-rs"
version = "0.1.0"
edition = "2024"

[dependencies]
anyhow = "1.0.100"
async-trait = "0.1.80"
pingora = { version = "0.6.0", features = ["lb"] }
serde = { version = "1.0.228", features = ["derive"] }
# pingora-core = "0.6.0"
# pingora-load-balancing = "0.6.0"
# pingora-proxy = "0.6.0"
tokio = { version = "1.47.1", features = ["full"] }
toml = "0.9.7"
tracing = "0.1.41"
tracing-core = "0.1.34"
tracing-subscriber = { version = "0.3.19", features = ["env-filter", "fmt"] }