use anyhow::Result;
use pingora::prelude::Opt;
use pingora::server::Server;
use proxy_rs::{config::Config, loadbalance, trace};
use tracing::{error, info};

fn main() -> Result<()> {
    trace::start_tracing()?;

    // 读取配置文件
    let config = match Config::load() {
        Ok(config) => {
            info!("✅ 成功加载配置文件");
            config
        }
        Err(e) => {
            error!("❌ 加载配置文件失败: {}", e);
            return Err(e);
        }
    };

    // 验证配置
    if let Err(e) = config.validate() {
        error!("❌ 配置验证失败: {}", e);
        return Err(e);
    }

    let mut server = Server::new(Some(Opt::parse_args())).unwrap();
    server.bootstrap();

    // 为每个服务创建独立的代理服务
    for (index, service) in config.loadbalance.service.iter().enumerate() {
        // 创建代理服务并添加到服务器
        loadbalance::create_proxy_service(&config, service, index, &mut server)
            .map_err(|e| {
                error!("❌ 创建服务 '{}' 失败: {}", service.name, e);
                e
            })?;
    }

    info!(
        "🚀 所有服务已启动，共 {} 个负载均衡服务",
        config.loadbalance.service.len()
    );
    server.run_forever();
}
