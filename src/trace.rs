use anyhow::Result;
use tracing::Level;
use tracing_subscriber::{
    EnvFilter, Layer as _, layer::SubscriberExt as _, util::SubscriberInitExt as _,
};

pub fn start_tracing() -> Result<()> {
    let env_filter = EnvFilter::builder()
        .with_default_directive(Level::DEBUG.into())
        // .parse(log_env + ",hyper=info,reqwest=info")
        .try_from_env()
        .unwrap_or_else(|_| EnvFilter::new("debug"));
    // let pid = std::process::id();

    let console_layer = tracing_subscriber::fmt::layer()
        .with_ansi(true)
        .with_level(true)
        .with_target(true)
        // .with_timer(CustomTime)
        .with_line_number(true)
        .with_thread_ids(true)
        .with_thread_names(true)
        .with_filter(env_filter);

    tracing_subscriber::registry()
        // .with(time_layer)
        .with(console_layer)
        .init();

    Ok(())
}
