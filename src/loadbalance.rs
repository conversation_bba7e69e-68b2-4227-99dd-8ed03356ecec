use std::sync::Arc;

use async_trait::async_trait;
use pingora::{
    Result,
    http::RequestHeader,
    lb::{LoadBalancer, selection::RoundRobin},
    prelude::{<PERSON>ttp<PERSON><PERSON>, Tcp<PERSON>ealthCheck, background_service},
    proxy::{ProxyHttp, Session, http_proxy_service},
    server::Server,
};
use tracing::info;
use crate::config::{Config, Service as ConfigService};

#[derive(Clone)]
pub struct LB {
    pub load_balancer: Arc<LoadBalancer<RoundRobin>>,
    pub tls_enabled: bool,
    pub sni: String,
}

#[async_trait]
impl ProxyHttp for LB {
    type CTX = ();

    fn new_ctx(&self) -> Self::CTX {}

    async fn upstream_peer(&self, _session: &mut Session, _ctx: &mut ()) -> Result<Box<HttpPeer>> {
        info!("🔍 Selecting upstream peer...");

        let upstream = self.load_balancer.select(b"", 256).unwrap();
        info!("✅ Selected upstream peer: {upstream:?}");

        let peer = Box::new(HttpPeer::new(
            upstream,
            self.tls_enabled,
            self.sni.clone(),
        ));

        if self.tls_enabled {
            info!("🔒 Created HTTPS HttpPeer for upstream");
        } else {
            info!("🚀 Created HTTP HttpPeer for upstream");
        }

        Ok(peer)
    }

    async fn upstream_request_filter(
        &self,
        _session: &mut Session,
        upstream_request: &mut RequestHeader,
        _ctx: &mut Self::CTX,
    ) -> Result<()> {
        upstream_request
            .insert_header("Host", &self.sni)
            .unwrap();
        Ok(())
    }
}

/// 创建代理服务并直接添加到服务器
///
/// 该函数封装了创建代理服务的所有逻辑，包括：
/// - 创建负载均衡器
/// - 设置健康检查
/// - 创建后台健康检查服务
/// - 创建 LB 实例
/// - 创建 HTTP 代理服务
/// - 配置监听地址
/// - 直接添加到服务器
///
/// 这种方式避免了类型擦除的问题，保持了原有的简洁性
pub fn create_proxy_service(
    config: &Config,
    service: &ConfigService,
    index: usize,
    server: &mut Server,
) -> anyhow::Result<()> {
    let service_name = &service.name;
    let listen_port = service.port.unwrap_or(8080 + index as u16);
    let backends = config.get_service_upstreams(service);
    let tls_enabled = config.is_tls_enabled(service);
    let sni = config.get_sni(service);

    info!(
        "🔧 配置服务 '{}': 监听端口 {}, TLS: {}, SNI: {}, 后端服务器: {:?}",
        service_name, listen_port, if tls_enabled { "启用" } else { "禁用" }, sni, backends
    );

    // 为每个服务创建独立的负载均衡器
    let mut upstreams = LoadBalancer::try_from_iter(backends.clone())
        .map_err(|e| anyhow::anyhow!("创建服务 '{}' 的负载均衡器失败: {}", service_name, e))?;

    // 设置健康检查
    let hc = TcpHealthCheck::new();
    upstreams.set_health_check(hc);
    upstreams.health_check_frequency = Some(std::time::Duration::from_secs(5));

    // 创建后台健康检查服务
    let background = background_service(&format!("health_check_{}", service_name), upstreams);
    let upstreams = background.task();

    // 创建负载均衡器实例，传递 TLS 和 SNI 配置
    let lb = LB {
        load_balancer: upstreams,
        tls_enabled,
        sni,
    };

    // 创建代理服务
    let mut proxy_service = http_proxy_service(&server.configuration, lb);
    let listen_addr = format!("0.0.0.0:{}", listen_port);
    proxy_service.add_tcp(&listen_addr);

    // 添加服务到服务器
    server.add_service(background);
    server.add_service(proxy_service);

    info!(
        "✅ 服务 '{}' 已配置完成，监听地址: {}, TLS: {}",
        service_name, listen_addr, if tls_enabled { "启用" } else { "禁用" }
    );

    Ok(())
}
