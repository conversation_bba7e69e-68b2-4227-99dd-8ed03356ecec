use anyhow::{Context, Result};
use serde::Deserialize;
use std::fs;

#[derive(Debug, Deserialize, Clone)]
pub struct Config {
    pub loadbalance: LoadBalance,
}

#[derive(Debug, Deserialize, Clone)]
pub struct LoadBalance {
    pub service: Vec<Service>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct Service {
    pub name: String,
    pub port: Option<u16>,
    pub tls: Option<bool>,
    pub sni: Option<String>,
    pub upstream: Vec<Upstream>,
}

#[derive(Debug, Deserialize, Clone)]
pub struct Upstream {
    pub host: String,
    pub port: u16,
}

impl Config {
    pub fn load() -> Result<Self> {
        let content = fs::read_to_string("config.toml")
            .context("无法读取 config.toml 文件")?;
        
        let config: Config = toml::from_str(&content)
            .context("无法解析 config.toml 文件")?;
        
        Ok(config)
    }

    pub fn validate(&self) -> Result<()> {
        if self.loadbalance.service.is_empty() {
            anyhow::bail!("配置中至少需要一个服务");
        }

        for service in &self.loadbalance.service {
            if service.name.is_empty() {
                anyhow::bail!("服务名称不能为空");
            }
            
            if service.upstream.is_empty() {
                anyhow::bail!("服务 '{}' 至少需要一个上游服务器", service.name);
            }

            for upstream in &service.upstream {
                if upstream.host.is_empty() {
                    anyhow::bail!("上游服务器主机地址不能为空");
                }
                
                if upstream.port == 0 {
                    anyhow::bail!("上游服务器端口不能为0");
                }
            }
        }

        Ok(())
    }

    pub fn get_service_upstreams(&self, service: &Service) -> Vec<String> {
        service
            .upstream
            .iter()
            .map(|upstream| format!("{}:{}", upstream.host, upstream.port))
            .collect()
    }

    pub fn is_tls_enabled(&self, service: &Service) -> bool {
        service.tls.unwrap_or(false)
    }

    pub fn get_sni(&self, service: &Service) -> String {
        service.sni.clone().unwrap_or_else(|| "one.one.one.one".to_string())
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_config_parsing() {
        let toml_content = r#"
[loadbalance]
[[loadbalance.service]]
name = "web-service"

[[loadbalance.service.upstream]]
host = "127.0.0.1"
port = 9000

[[loadbalance.service.upstream]]
host = "127.0.0.1"
port = 8000

[[loadbalance.service]]
name = "api-service"
sni = "custom.example.com"

[[loadbalance.service.upstream]]
host = "************"
port = 3000
"#;

        let config: Config = toml::from_str(toml_content).unwrap();
        assert_eq!(config.loadbalance.service.len(), 2);
        assert_eq!(config.loadbalance.service[0].name, "web-service");
        assert_eq!(config.loadbalance.service[0].upstream.len(), 2);
        assert_eq!(config.loadbalance.service[0].upstream[0].host, "127.0.0.1");
        assert_eq!(config.loadbalance.service[0].upstream[0].port, 9000);

        // 测试 SNI 配置
        assert_eq!(config.get_sni(&config.loadbalance.service[0]), "one.one.one.one"); // 默认值
        assert_eq!(config.get_sni(&config.loadbalance.service[1]), "custom.example.com"); // 自定义值
    }
}
